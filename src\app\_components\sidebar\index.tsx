"use client";
import React, { useState } from "react";
import { Sidebar, SidebarBody } from "@/components/ui/sidebar";

import { motion } from "motion/react";
import { cn } from "@heroui/theme";
import { mainPages } from "@/constant/site";
import SideBaritems from "./sidebar-items";
import { UsuIcon } from "@/components/icons";
import { AppNavbar } from "../navbar";

type Props = {
  children?: React.ReactNode;
};
export default function SidebarSection({ children }: Props) {
  const [open, setOpen] = useState(false);

  return (
    <div
      className={cn(
        "mx-auto w-full flex-1 flex-col overflow-hidden rounded-md border border-neutral-200 bg-default-200 md:flex-row dark:border-neutral-700",
        "min-h-screen"
      )}
    >
      <Sidebar open={open} setOpen={setOpen}>
        <AppNavbar />
        <SidebarBody className="justify-between gap-10 shadow-md ">
          <div className="flex flex-1 flex-col overflow-x-hidden overflow-y-auto">
            <div className="block md:hidden">
              <Logo />
            </div>
            <div className="flex flex-col gap-2">
              <SideBaritems items={mainPages} />
            </div>
          </div>
        </SidebarBody>
        <main className=" md:pl-20 px-5 mt-16 md:mt-20 ">{children}</main>
      </Sidebar>
    </div>
  );
}
export const Logo = () => {
  return (
    <a
      href="#"
      className="relative z-20  flex items-center space-x-2 py-1 text-sm font-normal text-black"
    >
      <div>
        <UsuIcon size={38} className="text-default-900" />
      </div>
      <motion.span
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="font-bold text-lg whitespace-pre text-black dark:text-white"
      >
        POLIKLINIK
      </motion.span>
    </a>
  );
};
