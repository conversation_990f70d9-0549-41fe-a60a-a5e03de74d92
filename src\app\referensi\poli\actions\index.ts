import fetcher from "@/libs/fetch-api";
import { ResponsePoli } from "../_schema/typePoli";

export async function getPoli() {
  return fetcher<ResponsePoli>({
    path: `/reference/polyclinic`,
    options: {
      next: {
        tags: ["poli-estetika-2"],
      },
    },
  });
}

export async function postPoli({
  name,
  active,
}: {
  name?: string | null;
  active?: boolean | null;
}) {
  return fetcher<any[]>({
    path: `/reference/polyclinic`,
    options: {
      method: "POST",
      body: JSON.stringify({ name, active }),
    },
  });
}

export async function patchtPoli({
  name,
  active,
  id,
}: {
  name?: string | null;
  active?: boolean | null;
  id: string;
}) {
  return fetcher<any[]>({
    path: `/reference/polyclinic/${id}`,
    options: {
      next: {
        tags: ["poli-estetika-2"],
      },
      method: "PATCH",
      body: JSON.stringify({ name, active }),
    },
  });
}
