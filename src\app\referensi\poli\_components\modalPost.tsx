"use client";

import { FormInput } from "@/components/custom/input";
import {
  addToast,
  <PERSON><PERSON>,
  <PERSON>dal,
  <PERSON>dal<PERSON>ody,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Plus } from "lucide-react";
import { poliPost } from "../_schema/postPoli";
import { postPoli } from "../actions";

type payload = {
  name: string;
};

export default function ModalPost() {
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  const initialValues = {
    name: "",
  };

  const handleSubmit = async (value: typeof initialValues) => {
    formik.setSubmitting(true);
    try {
      const res = await postPoli({ name: value.name, active: null });
      if (res.error) {
        throw new Error(res?.message);
      }
      addToast({
        title: "Berhasil menambahkan data",
        color: "success",
      });
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "<PERSON><PERSON><PERSON><PERSON> k<PERSON>",
      });
    } finally {
      formik.setSubmitting(false);
    }
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: poliPost,
    onSubmit: handleSubmit,
  });

  return (
    <>
      <Tooltip content="Edit Data">
        <Button
          startContent={<Plus />}
          color="primary"
          size="sm"
          onPress={onOpenChange}
        >
          Poli
        </Button>
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah Poli
                </ModalHeader>
                <ModalBody className="mb-4">
                  <div className="flex flex-col gap-1">
                    <label htmlFor="poli" className="text-sm ">
                      {" "}
                      Nama Poli
                    </label>
                    <FormInput
                      isClearable={false}
                      name={"name"}
                      isNumeric={false}
                      formik={formik}
                      placeholder="Ketik nama poli"
                    />
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    // onPress={onClose}
                  >
                    Kembali
                  </Button>
                  <Button
                    color="primary"
                    type="submit"
                    isLoading={formik.isSubmitting}
                  >
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
