"use client";

import UTable from "@/components/custom/table";
import {
    <PERSON><PERSON>,
    <PERSON>ner,
    Switch,
    TableBody,
    TableCell,
    TableColumn,
    TableHeader,
    TableRow,
    Tooltip,
} from "@heroui/react";
import { Eye } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";

export const users = [];

const columns = [
    {
        key: "jenis",
        label: "Jenis <PERSON>emeri<PERSON>",
    },
    {
        key: "nilai",
        label: "Nilai Rujukan",
    },
    {
        key: "satuan",
        label: "Satuan",
    },
    {
        key: "action",
        label: "Aksi",
    },
];

export default function TableParameter() {
    const [page, setPage] = useState(0);
    const router = useRouter();

    const pagination = {
        page: 1,
        pageSize: 10,
        totalPage: 2,
        totalData: 20,
        onChangePage: (page: number, pageSize: number) => setPage(page),
    };

    const renderCell = useCallback(
        (category: any, column_key: any, index: number) => {
            switch (column_key) {
                case "no":
                    return index + 1;
                case "name":
                    return <div className="">{category.name}</div>;
                case "status":
                    return (
                        <div className="flex gap-2 justify-center items-center ">
                            <Switch
                                size="sm"
                                defaultSelected={category.status === "active"}
                            />
                            <p className="capitalize">{category.status}</p>
                        </div>
                    );
                case "action":
                    return (
                        <div>
                            <Tooltip content="Lihat Detail">
                                <Button
                                    isIconOnly
                                    color="secondary"
                                    size="sm"
                                    onPress={() =>
                                        router.push(`/referensi/pemeriksaan-lab/${category.id}`)
                                    }
                                >
                                    <Eye className="w-4" />
                                </Button>
                            </Tooltip>
                        </div>
                    );
                default:
                    return category[column_key];
            }
        },
        []
    );

    return (
        <div className="bg-default-50  rounded-md ">
            <UTable bordered={false} parameter={true}>
                <TableHeader columns={columns}>
                    {(column) => (
                        <TableColumn
                            align={
                                column.key === "action" || column.key === "status"
                                    ? "end"
                                    : "start"
                            }
                            key={column.key}
                        >
                            {column.label}
                        </TableColumn>
                    )}
                </TableHeader>
                <TableBody
                    items={users}
                    emptyContent="No rows to display"
                    loadingContent={<Spinner />}
                    isLoading={false}
                >
                    {users.map((item, index) => (
                        <TableRow key={index}>
                            {(columnKey) => (
                                <TableCell>{renderCell(item, columnKey, index)}</TableCell>
                            )}
                        </TableRow>
                    ))}
                </TableBody>
            </UTable>
        </div>
    );
}
