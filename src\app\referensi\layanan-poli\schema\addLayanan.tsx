import * as yup from "yup";

export const layananAddSchema = yup.object({
  laboratorium: yup.string().required("Nama layanan wajib diisi"),
  pasienPengguna: yup
    .array()
    .min(1, "Pilih minimal satu jenis pasien")
    .required("Jenis pasien wajib dipilih"),
  tarifMahasiswa: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("1"),
    then: (schema) =>
      schema
        .required("Tarif untuk mahasiswa wajib diisi")
        .min(0, "Tarif tidak boleh negatif"),
    otherwise: (schema) => schema.notRequired(),
  }),
  tarifPegawai: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("2"),
    then: (schema) =>
      schema
        .required("Tarif untuk pegawai wajib diisi")
        .min(0, "Tarif tidak boleh negatif"),
    otherwise: (schema) => schema.notRequired(),
  }),
  // Tambahkan validasi untuk tarif lainnya sesuai kebutuhan
});

export type LayananAddSchema = yup.InferType<typeof layananAddSchema>;
