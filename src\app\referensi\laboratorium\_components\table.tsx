"use client";

import UTable from "@/components/custom/table";
import {
  <PERSON><PERSON>,
  <PERSON>witch,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { useCallback, useState } from "react";
import Modals from "./modal";
import ModalPost from "./modalPost";

export const users = [
  {
    id: 1,
    name: "<PERSON>",
    status: "active",
  },
  {
    id: 2,
    name: "<PERSON>",
    status: "active",
  },
  {
    id: 3,
    name: "<PERSON>",
    status: "active",
  },
];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Laboratorium",
  },
  {
    key: "status",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

export default function TableLaboratorium() {
  const [page, setPage] = useState(0);

  const pagination = {
    page: 1,
    pageSize: 10,
    totalPage: 2,
    totalData: 20,
    onChangePage: (page: number, pageSize: number) => setPage(page),
  };

  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <div className="">{category.name}</div>;
        case "status":
          return (
            <div className="flex gap-2 justify-center items-center ">
              <Switch
                size="sm"
                defaultSelected={category.status === "active"}
              />
              <p className="capitalize">{category.status}</p>
            </div>
          );
        case "action":
          return <Modals id={category.id} />;
        default:
          return category[column_key];
      }
    },
    []
  );

  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Laboratorium</h1>
        <ModalPost />
      </div>
      <UTable pagination={pagination} bordered={true}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              align={
                column.key === "action" || column.key === "status"
                  ? "center"
                  : "start"
              }
              key={column.key}
            >
              {column.label}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={users}
          emptyContent="No rows to display"
          loadingContent={<Spinner />}
          isLoading={false}
        >
          {users.map((item, index) => (
            <TableRow key={item.id}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey, index)}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </UTable>
    </div>
  );
}
