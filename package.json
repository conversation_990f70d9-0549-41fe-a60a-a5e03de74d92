{"name": "template-frontend", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx -c .eslintrc.json --fix"}, "dependencies": {"@heroui/react": "^2.7.5", "@heroui/system": "2.4.15", "@heroui/theme": "2.4.15", "@internationalized/date": "^3.7.0", "@psi/react-access-control": "^0.1.2", "@psi/sso-auth": "^0.1.1", "@react-aria/i18n": "^3.12.8", "@react-aria/ssr": "3.9.8", "@react-aria/visually-hidden": "3.8.22", "@react-pdf/renderer": "^4.2.2", "@react-stately/data": "^3.11.7", "@tanstack/react-table": "^8.20.5", "apexcharts": "^4.1.0", "axios": "^1.7.7", "dayjs": "^1.11.13", "formik": "^2.4.6", "lucide-react": "^0.503.0", "motion": "^12.18.1", "next": "15.3.0", "next-nprogress-bar": "^2.4.7", "next-themes": "^0.4.6", "rc-tree": "^5.9.0", "rc-tree-select": "^5.27.0", "react": "19.1.0", "react-apexcharts": "^1.7.0", "react-dom": "19.1.0", "swr": "^2.3.3", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "yup": "^1.4.0"}, "devDependencies": {"@types/node": "20.5.7", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "@typescript-eslint/eslint-plugin": "8.31.1", "@typescript-eslint/parser": "8.31.1", "autoprefixer": "10.4.19", "eslint": "^8.57.0", "eslint-config-next": "15.3.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-boundaries": "^5.0.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "8.4.38", "tailwind-variants": "0.1.20", "tailwindcss": "3.4.3", "typescript": "5.2.2"}, "overrides": {"@types/react": "19.1.0", "@types/react-dom": "19.1.2"}}