import FilterPoli from "./_components/filter";
import Table<PERSON>oli from "./_components/table";
import { ResponsePoli } from "./_schema/typePoli";
import { getPoli } from "./actions";

export default async function PagePoli() {
  const resPoli = getPoli();

  // const onChangeSwitch = async () => {};

  return (
    <div className="flex flex-col gap-4">
      <FilterPoli title="Poli" />
      {/* {JSON.stringify(resPoli)} */}
      <div>
        <TablePoli poli={resPoli} />
      </div>
    </div>
  );
}
