import * as yup from "yup";

export const layananAddSchema = yup.object({
  laboratorium: yup.string().required("Nama layanan wajib diisi"),
  pasienPengguna: yup
    .array()
    .min(1, "Pilih minimal satu jenis pasien")
    .required("Jenis pasien wajib dipilih"),
  tarifMahasiswa: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("1"),
    then: (schema) =>
      schema
        .required("Tarif untuk mahasiswa wajib diisi")
        .min(0, "Tarif tidak boleh negatif"),
    otherwise: (schema) => schema.notRequired(),
  }),
  tarifPegawai: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("2"),
    then: (schema) =>
      schema
        .required("Tarif untuk pegawai wajib diisi")
        .min(0, "Tarif tidak boleh negatif"),
    otherwise: (schema) => schema.notRequired(),
  }),
    parameters: yup.array().of(
    yup.object({
      parameterName: yup.string().required('Nama parameter wajib diisi'),
      listParameters: yup.array().of(
        yup.object({
          name: yup.string().required('Nama sub-parameter wajib diisi'),
          jenis: yup.string().required('Jenis pemeriksaan wajib diisi'),
          nilai: yup.string().required('Nilai wajib diisi'),
          satuan: yup.string().required('Satuan wajib diisi'),
        })
      ).min(1, 'Minimal 1 sub-parameter harus ditambahkan'),
    })
  ).min(1, 'Minimal 1 parameter harus ditambahkan'),
  // Tambahkan validasi untuk tarif lainnya sesuai kebutuhan
});

export type LayananAddSchema = yup.InferType<typeof layananAddSchema>;
