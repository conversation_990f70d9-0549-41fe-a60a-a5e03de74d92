"use client";

import { FormInput } from "@/components/custom/input";
import { Button, Checkbox, CheckboxGroup, Tooltip } from "@heroui/react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { PATIENT_TYPE_LIST } from "@/constant/constant";
import { layananAddSchema } from "../schema/addLayanan";
import TableParameter from "./tableParameter";
import { Plus } from "lucide-react";


interface ListParameterItem {
  name: string;
  jenis: string;
  nilai: string;
  satuan: string;
}

interface Parameter {
  parameterName: string;
  listParameters: ListParameterItem[];
}


type FormValues = {
  laboratorium: string;
  pasienPengguna: string[];
  tarifMahasiswa: string;
  tarifPegawai: string;
  tarifKeluarga: string;
  tarifUmum: string;
  tarifAlumni: string;
  tarifPensiunan: string;
  parameters: Parameter[];
};

export default function FormAddLayanan() {
  const initialValues: FormValues = {
    laboratorium: "",
    pasienPengguna: [],
    tarifMahasiswa: "",
    tarifPegawai: "",
    tarifKeluarga: "",
    tarifUmum: "",
    tarifAlumni: "",
    tarifPensiunan: "",
    parameters: [],
  };

  const router = useRouter();

  const formik = useFormik({
    initialValues,
    validationSchema: layananAddSchema,
    onSubmit: (values) => {
      formik.setSubmitting(true);
      console.log(values);
      formik.setSubmitting(false);
    },
  });

  // Handler untuk checkbox
  const handleCheckboxChange = (selectedValues: string[]) => {
    formik.setFieldValue("pasienPengguna", selectedValues);
  };


  const getTarifFieldName = (key: string): string => {
    switch (key) {
      case PATIENT_TYPE_LIST[0].value:
        return "tarifMahasiswa";
      case PATIENT_TYPE_LIST[1].value:
        return "tarifPegawai";
      case PATIENT_TYPE_LIST[2].value:
        return "tarifKeluarga";
      case PATIENT_TYPE_LIST[3].value:
        return "tarifUmum";
      case PATIENT_TYPE_LIST[4].value:
        return "tarifAlumni";
      case PATIENT_TYPE_LIST[5].value:
        return "tarifPensiunan";
      default:
        return "";
    }
  };

  // Handler untuk menambah parameter baru
  const addParameter = () => {
    const newParameter: Parameter = {
      parameterName: "",
      listParameters: [],
    };
    formik.setFieldValue("parameters", [...formik.values.parameters, newParameter]);
  };

  // Handler untuk menghapus parameter
  const removeParameter = (index: number) => {
    const updatedParameters = formik.values.parameters.filter((_, i) => i !== index);
    formik.setFieldValue("parameters", updatedParameters);
  };

  // Handler untuk menambah list parameter
  const addListParameter = (paramIndex: number) => {
    const updatedParameters = [...formik.values.parameters];
    updatedParameters[paramIndex] = {
      ...updatedParameters[paramIndex],
      listParameters: [
        ...updatedParameters[paramIndex].listParameters,
        { name: "", jenis: "", nilai: "", satuan: "" },
      ],
    };
    formik.setFieldValue("parameters", updatedParameters);
  };

  // Handler untuk menghapus list parameter
  const removeListParameter = (paramIndex: number, listIndex: number) => {
    const updatedParameters = [...formik.values.parameters];
    updatedParameters[paramIndex] = {
      ...updatedParameters[paramIndex],
      listParameters: updatedParameters[paramIndex].listParameters.filter(
        (_, i) => i !== listIndex
      ),
    };
    formik.setFieldValue("parameters", updatedParameters);
  };

  // Handler untuk perubahan input parameter utama
  const handleParameterChange = (
    paramIndex: number,
    field: keyof Parameter,
    value: string
  ) => {
    const updatedParameters = [...formik.values.parameters];
    updatedParameters[paramIndex] = {
      ...updatedParameters[paramIndex],
      [field]: value,
    };
    formik.setFieldValue("parameters", updatedParameters);
  };

  // Handler untuk perubahan input list parameter
  const handleListParameterChange = (
    paramIndex: number,
    listIndex: number,
    field: keyof ListParameterItem,
    value: string
  ) => {
    const updatedParameters = [...formik.values.parameters];
    updatedParameters[paramIndex].listParameters[listIndex] = {
      ...updatedParameters[paramIndex].listParameters[listIndex],
      [field]: value,
    };
    formik.setFieldValue("parameters", updatedParameters);
  };

  return (
    <form onSubmit={formik.handleSubmit}>
      <div className="rounded-md w-full flex flex-col gap-4 justify-center items-center">
        <div className="flex-col gap-6 flex bg-default-50 lg:w-1/2 w-full p-6 rounded-md">
          <h1 className="text-xl font-medium">Form Tambah Layanan</h1>

          {/* Field Nama Layanan */}
          <div className="flex flex-col gap-1">
            <label htmlFor="laboratorium" className="text-sm">
              Nama Layanan
            </label>
            <FormInput
              name="laboratorium"
              isNumeric={false}
              formik={formik}
              placeholder="Ketik nama laboratorium"
            />
          </div>

          {/* Pasien Pengguna Layanan */}
          <div className="flex flex-col gap-2">
            <label className="text-sm">Pasien Pengguna Layanan</label>
            <CheckboxGroup
              size="sm"
              value={formik.values.pasienPengguna}
              onChange={handleCheckboxChange}
              name="pasienPengguna"
            >
              {PATIENT_TYPE_LIST.map((item) => (
                <Checkbox key={item.value} value={item.value}>
                  {item.label}
                </Checkbox>
              ))}
            </CheckboxGroup>
            {formik.touched.pasienPengguna && formik.errors.pasienPengguna && (
              <div className="text-red-500 text-sm">
                {formik.errors.pasienPengguna}
              </div>
            )}
          </div>

          <hr />

          {/* Section Tarif */}
          <div className="flex flex-col gap-2">
            <h1 className="font-medium">Tarif</h1>
            {formik.values.pasienPengguna.length === 0 && (
              <p className="text-sm text-default-500">
                Pilih pasien pengguna layanan terlebih dahulu untuk menentukan
                tarif!
              </p>
            )}
          </div>

          {/* Conditional Tarif Fields */}
          <div className="flex flex-col gap-6">
            {formik.values.pasienPengguna.length > 0 &&
              formik.values.pasienPengguna.map((key, index) => (
                <div className="flex flex-col gap-1" key={key}>
                  <label
                    htmlFor={getTarifFieldName(key) as string}
                    className="text-sm"
                  >
                    {getTarifFieldName(key).split("tarif")}
                  </label>
                  <FormInput
                    name={getTarifFieldName(key) as any}
                    isNumeric={true}
                    formik={formik}
                    placeholder="Rp.0"
                  />
                </div>
              ))}
          </div>

        </div>
        <div className="flex-col gap-6 flex bg-default-50 lg:w-1/2 w-full p-6 rounded-md">
              <div className="flex justify-end">
          <Button
            type="button"
            // startContent={<PlusIcon className="h-5 w-5" />}
            onPress={addParameter}
          >
            Tambah Parameter
          </Button>
        </div>

        {/* List Parameters */}
        {formik.values.parameters.map((parameter, paramIndex) => (
          <div key={paramIndex} className="border rounded-lg p-4 space-y-4 bg-gray-50">
            {/* Parameter Header */}
            <div className="flex justify-between items-center">
              <h2 className="font-medium text-lg">Parameter {paramIndex + 1}</h2>
              <Button
                type="button"
                variant="light"
                color="danger"
             
                onPress={() => removeParameter(paramIndex)}
              >
                hapus
                {/* <TrashIcon className="h-5 w-5" /> */}
              </Button>
            </div>

            {/* Parameter Name */}
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Nama Parameter Utama</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border rounded-md"
                  value={parameter.parameterName}
                  onChange={(e) =>
                    handleParameterChange(paramIndex, "parameterName", e.target.value)
                  }
                  placeholder="Contoh: Hematologi"
                />
              </div>
            </div>

            {/* List Parameters */}
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-medium">Daftar Sub-Parameter</h3>
                <Button
                  type="button"
                  size="sm"
                  // startContent={<PlusIcon className="h-4 w-4" />}
                  onPress={() => addListParameter(paramIndex)}
                >
                  Tambah Sub-Parameter
                </Button>
              </div>

              {parameter.listParameters.map((listParam, listIndex) => (
                <div key={listIndex} className="border p-4 rounded-md bg-white space-y-3">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">Sub-Parameter {listIndex + 1}</h4>
                    <Button
                      type="button"
                      size="sm"
                      variant="light"
                      color="danger"
                      // isIconOnly
                      onPress={() => removeListParameter(paramIndex, listIndex)}
                    >
                      {/* <TrashIcon className="h-4 w-4" /> */}
                      hapus
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Nama</label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border rounded-md"
                        value={listParam.name}
                        onChange={(e) =>
                          handleListParameterChange(
                            paramIndex,
                            listIndex,
                            "name",
                            e.target.value
                          )
                        }
                        placeholder="Contoh: Hemoglobin"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Jenis</label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border rounded-md"
                        value={listParam.jenis}
                        onChange={(e) =>
                          handleListParameterChange(
                            paramIndex,
                            listIndex,
                            "jenis",
                            e.target.value
                          )
                        }
                        placeholder="Contoh: Darah Rutin"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Nilai</label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border rounded-md"
                        value={listParam.nilai}
                        onChange={(e) =>
                          handleListParameterChange(
                            paramIndex,
                            listIndex,
                            "nilai",
                            e.target.value
                          )
                        }
                        placeholder="Contoh: 12.5"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Satuan</label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border rounded-md"
                        value={listParam.satuan}
                        onChange={(e) =>
                          handleListParameterChange(
                            paramIndex,
                            listIndex,
                            "satuan",
                            e.target.value
                          )
                        }
                        placeholder="Contoh: g/dL"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
          {/* <div className="flex flex-col gap-2">
            <h1 className="text-lg font-medium">Parameter</h1>
            <div className="">
              <Tooltip content="Tambah Data">
                <Button
                  startContent={<Plus className="w-4" />}
                  color="default"
                  className="bg-default-50 border-2"
                  size="sm"
                // onPress={onOpenChange}
                >
                  Tambah Jenis Pemeriksaan
                </Button>
              </Tooltip>
            </div>
          </div>
          <div className="">
            < TableParameter />
          </div> */}
        </div>


      </div>
      <div className="rounded-md w-full flex flex-col gap-4 items-center">

        <div className="flex lg:w-1/2 w-full">
          <div className="flex  gap-4  justify-start mt-4 w-full lg:w-1/2 ">

            <Button
              startContent={<Plus className="w-4" />}
              color="primary"

            >
              Tambah Parameter
            </Button>


          </div>
          <div className="flex  gap-4 justify-end mt-4 w-full lg:w-1/2 ">
            <Button
              className={`bg-default-50 border-[1px] border-md`}
              type="button"
              onPress={() => {
                formik.resetForm();
                router.back();
              }}
            >
              Kembali
            </Button>
            <Button
              className={`${!formik.isValid && "bg-default-500"}`}
              color="primary"
              type="submit"
              isLoading={formik.isSubmitting}
              disabled={formik.isSubmitting || !formik.isValid}
            >
              Simpan
            </Button>
          </div>
        </div>


      </div>
    </form>
  );
}