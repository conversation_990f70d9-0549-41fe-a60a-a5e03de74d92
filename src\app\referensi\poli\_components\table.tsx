"use client";

import UTable from "@/components/custom/table";
import {
  <PERSON><PERSON>,
  Switch,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { use, useCallback, useState } from "react";
import { ResponsePoli } from "../_schema/typePoli";
import Modals from "./modal";
import ModalPost from "./modalPost";

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Poli",
  },
  {
    key: "active",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

type Props = {
  poli: any;
  onChangeSwitch?: (id: number) => void;
};

export default function TablePoli({ poli }: Props) {
  const [page, setPage] = useState(0);
  const data: ResponsePoli = use(poli);

  const pagination = {
    page: page,
    pageSize: data?.page?.size ?? 10,
    totalPage: data?.page?.total_pages ?? 10,
    totalData: data?.page?.total_elements ?? 10,
    onChangePage: (page: number, pageSize: number) => setPage(page),
  };

  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <div className="">{category.name}</div>;
        case "status":
          return (
            <div className="flex gap-2 justify-center items-center ">
              <Switch
                size="sm"
                defaultSelected={category.active === "active"}
              />
              <p className="capitalize">{category.active}</p>
            </div>
          );
        case "action":
          return <Modals id={category.id} />;
        default:
          return category[column_key];
      }
    },
    []
  );

  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Poli</h1>
        <ModalPost />
      </div>
      <UTable pagination={pagination} bordered={true}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              align={
                column.key === "action" || column.key === "active"
                  ? "center"
                  : "start"
              }
              key={column.key}
            >
              {column.label}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={data.content ?? []}
          emptyContent="No rows to display"
          loadingContent={<Spinner />}
          isLoading={false}
        >
          {data?.content?.map((item, index) => (
            <TableRow key={item.id}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey, index)}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </UTable>
    </div>
  );
}
