"use client";
import React from "react";
import Link from "next/link";
import { useSelectedLayoutSegments, useParams } from "next/navigation";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/react";
import siteConfig from "@/constant/site";

// Helper: cek apakah segmen config cocok dengan segmen URL (support param)
const isMatch = (
  configSegment: string | string[],
  currentSegments: string[],
  params: Record<string, string>
) => {
  const normalizedConfig =
    typeof configSegment === "string" ? [configSegment] : configSegment;

  if (normalizedConfig.length > currentSegments.length) return false;

  return normalizedConfig.every((seg, i) => {
    const current = currentSegments[i];

    if (Object.values(params).includes(current)) {
      const paramKey = Object.keys(params).find(
        (key) => params[key] === current
      );

      return seg === paramKey;
    }

    return seg === current;
  });
};

export default function BreadcrumbsSection() {
  const segments = useSelectedLayoutSegments().filter((s) => !s.includes("("));
 
  const params = useParams();

  const breadcrumbs = siteConfig.breadcrumbs
    .filter((item) =>
      isMatch(item.segment, segments, params as Record<string, string>)
    )
    .map((item, index) => {
      const href = "/" + segments.slice(0, index + 1).join("/");

      return { label: item.label, href };
    });



  return (
    <Breadcrumbs
      className="py-3"
      itemsAfterCollapse={2}
      itemsBeforeCollapse={1}
    >
      <BreadcrumbItem isCurrent={segments.length === 0}>
        <Link href="/">Home</Link>
      </BreadcrumbItem>

      {breadcrumbs.map(({ label, href }) => (
        <BreadcrumbItem key={href}>
          <Link href={href}>{label}</Link>
        </BreadcrumbItem>
      ))}
    </Breadcrumbs>
  );
}
