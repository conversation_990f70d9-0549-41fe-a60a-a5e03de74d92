"use client";

import { FormInput } from "@/components/custom/input";
import { Button, Checkbox, CheckboxGroup } from "@heroui/react";
import { useFormik } from "formik";

import { layananAddSchema } from "@/app/referensi/layanan-poli/schema/addLayanan";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { PATIENT_TYPE_LIST } from "@/constant/constant";

const dumyApi = {
  laboratorium: "dumy",
  pasienPengguna: ["1", "2", "5"],
  tarifMahasiswa: "200",
  tarifPegawai: "2000",
  tarifKeluarga: "1000",
  tarifUmum: "",
  tarifAlumni: "5000",
  tarifPensiunan: "",
};

export default function FormUpdateLayanan() {
  const initialValues = {
    laboratorium: "",
    pasienPengguna: [] as string[],
    tarifMahasiswa: "",
    tarifPegawai: "",
    tarifKeluarga: "",
    tarifUmum: "",
    tarif<PERSON>lumni: "",
    tarifPensiunan: "",
  };

  const router = useRouter();

  const handleSubmit = (values: typeof initialValues) => {
    formik.setSubmitting(true);
    console.log(values);
    formik.setSubmitting(false);
  };

  const formik = useFormik({
    initialValues,
    validationSchema: layananAddSchema,
    onSubmit: handleSubmit,
  });

  const handleCheckboxChange = (selectedValues: string[]) => {
    formik.setFieldValue("pasienPengguna", selectedValues);
  };

  const getTarifFieldName = (key: string): string => {
    switch (key) {
      case PATIENT_TYPE_LIST[0].value:
        return "tarifMahasiswa";
      case PATIENT_TYPE_LIST[1].value:
        return "tarifPegawai";
      case PATIENT_TYPE_LIST[2].value:
        return "tarifKeluarga";
      case PATIENT_TYPE_LIST[3].value:
        return "tarifUmum";
      case PATIENT_TYPE_LIST[4].value:
        return "tarifAlumni";
      case PATIENT_TYPE_LIST[5].value:
        return "tarifPensiunan";
      default:
        return "";
    }
  };
  useEffect(() => {
    formik.setValues(dumyApi);
  }, []);

  return (
    <form onSubmit={formik.handleSubmit}>
      <div className="rounded-md w-full flex flex-col justify-center items-center">
        <div className="flex-col gap-6 flex bg-default-50 lg:w-1/2 p-6 w-full rounded-md">
          <h1 className="text-xl font-medium">Form Update Layanan</h1>

          {/* Field Nama Layanan */}
          <div className="flex flex-col gap-1">
            <label htmlFor="laboratorium" className="text-sm">
              Nama Layanan
            </label>
            <FormInput
              name="laboratorium"
              isNumeric={false}
              formik={formik}
              placeholder="Ketik nama laboratorium"
            />
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-sm">Pasien Pengguna Layanan</label>
            <CheckboxGroup
              size="sm"
              value={formik.values.pasienPengguna}
              onChange={handleCheckboxChange}
              name="pasienPengguna"
            >
              {PATIENT_TYPE_LIST.map((item) => (
                <Checkbox key={item.value} value={item.value}>
                  {item.label}
                </Checkbox>
              ))}
            </CheckboxGroup>
            {formik.touched.pasienPengguna && formik.errors.pasienPengguna && (
              <div className="text-red-500 text-sm">
                {formik.errors.pasienPengguna}
              </div>
            )}
          </div>

          <hr />

          {/* Section Tarif */}
          <div className="flex flex-col gap-2">
            <h1 className="font-medium">Tarif</h1>
            {formik.values.pasienPengguna.length === 0 && (
              <p className="text-sm text-default-500">
                Pilih pasien pengguna layanan terlebih dahulu untuk menentukan
                tarif!
              </p>
            )}
          </div>

          {/* Conditional Tarif Fields */}
          <div className="flex flex-col gap-6">
            {formik.values.pasienPengguna.length > 0 &&
              formik.values.pasienPengguna.map((key, index) => (
                <div className="flex flex-col gap-1" key={key}>
                  <label
                    htmlFor={getTarifFieldName(key) as string}
                    className="text-sm"
                  >
                    {getTarifFieldName(key).split("tarif")}
                  </label>
                  <FormInput
                    name={getTarifFieldName(key) as any}
                    isNumeric={true}
                    formik={formik}
                    placeholder="Rp.0"
                  />
                </div>
              ))}

            <div className="flex items-center gap-4 justify-end mt-4">
              <Button
                className={`bg-default-50 border-[1px] border-md `}
                type="button"
                onPress={() => {
                  formik.resetForm();
                  router.back();
                }}
              >
                Kembali
              </Button>
              <Button
                className={`${!formik.isValid && "bg-default-500"}`}
                color="primary"
                type="submit"
                isLoading={formik.isSubmitting}
                disabled={formik.isSubmitting || !formik.isValid}
              >
                Simpan
              </Button>
            </div>
          </div>
        </div>
      </div>
    </form>
  );
}
