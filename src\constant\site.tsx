import { RouteItem, SiteConfig } from "@/types/site-config";
import { ClipboardPlus, Home, Settings, User, UserCog } from "lucide-react";

export const mainPages: RouteItem[] = [
  {
    key: "/home",
    path: "/home",
    title: "Home",
    icon: <Home />,
    access: ["*"],
    segment: [["home"]],
  },
  {
    key: "/registrasi",
    type: "divider",
    title: "REGISTRASI",
    path: "#",
    segment: [["registrasi"]],
  },
  {
    key: "/kunjungan",
    path: "/kunjungan",
    title: "Kunjungan",
    icon: <ClipboardPlus />,
    access: ["*"],
    segment: [["kunjungan"]],
  },
  {
    key: "/referensi",
    path: "/referensi",
    title: "Referensi",
    icon: <ClipboardPlus />,
    access: ["*"],
    segment: [["referensi"]],
    type: "subMenu",
    children: [
      {
        key: "/referensi/poli",
        icon: <UserCog />,
        path: "/referensi/poli",
        title: "Poli",
        parent: "/poli",
        access: ["role.*", "role.get"],
        segment: [["referensi", "poli"]],
      },
      {
        key: "/referensi/laboratorium",
        icon: <UserCog />,
        path: "/referensi/laboratorium",
        title: "Laboratorium",
        parent: "/laboratorium",
        access: ["role.*", "role.get"],
        segment: [["referensi", "laboratorium"]],
      },
      {
        key: "/referensi/layanan-poli",
        icon: <UserCog />,
        path: "/referensi/layanan-poli",
        title: "Layanan Poli",
        parent: "/layanan-poli",
        access: ["role.*", "role.get"],
        segment: [["referensi", "layanan-poli"]],
      },
      {
        key: "/referensi/dokter",
        icon: <UserCog />,
        path: "/referensi/dokter",
        title: "Tenaga Medis",
        parent: "/dokter",
        access: ["role.*", "role.get"],
        segment: [["referensi", "dokter"]],
      },
      {
        key: "/referensi/pemeriksaan-lab",
        icon: <UserCog />,
        path: "/referensi/pemeriksaan-lab",
        title: "Pemeriksaan Lab",
        parent: "/pemeriksaan-lab",
        access: ["role.*", "role.get"],
        segment: [["referensi", "pemeriksaan-lab"]],
      },
    ],
  },
  {
    key: "/setting",
    path: "/setting",
    title: "Pengguna",
    icon: <Settings />,
    segment: [["setting"]],
    type: "subMenu",
    children: [
      {
        key: "/setting/role",
        icon: <UserCog />,
        path: "/setting/role",
        title: "Hak Akses",
        parent: "/setting",
        access: ["role.*", "role.get"],
        segment: [["setting", "role"]],
      },
      {
        key: "/setting/user",
        path: "/setting/user",
        icon: <User />,
        title: "Pengguna",
        parent: "/setting",
        access: ["user.*", "user.get"],
        segment: [["setting", "user"]],
      },
    ],
  },
];

export const siteConfig: SiteConfig = {
  name: "POLIKLINIK USU",
  description: "Medical Record POLIKLINIK Universitas Sumatera Utara",
  pages: mainPages,
  mobilePages: mainPages,
  breadcrumbs: [
    {
      label: "Beranda",
      segment: ["admin", "home"],
    },
    {
      label: "Peran",
      segment: ["admin", "role"],
    },
    {
      label: "Pengguna",
      segment: ["admin", "user"],
    },
  ],
};

export default siteConfig;
